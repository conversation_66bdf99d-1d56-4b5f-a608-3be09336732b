#!/usr/bin/env python3
"""
Calculate 67^67^67 (67 to the power of 67 to the power of 67)

This is an astronomically large number with over 400 billion billion billion digits.
Since computing all digits is impossible, we calculate the first few digits using
logarithmic methods and provide information about the number's magnitude.
"""

from decimal import Decimal, getcontext

def calculate_67_power_tower():
    """
    Calculate properties of 67^67^67 = 67^(67^67)
    Returns the number of digits and first few digits
    """
    print("Calculating 67^67^67...")
    
    # First calculate 67^67
    inner_exp = 67 ** 67
    print(f"Step 1: 67^67 = {inner_exp}")
    print(f"        This has {len(str(inner_exp))} digits")
    
    # Set high precision for logarithmic calculations
    original_prec = getcontext().prec
    getcontext().prec = 1000
    
    try:
        # Calculate log10(67^67^67) = (67^67) * log10(67)
        base = Decimal(67)
        log_base = base.ln() / Decimal(10).ln()  # log10(67)
        inner_exp_decimal = Decimal(str(inner_exp))
        log_val = inner_exp_decimal * log_base
        
        # Number of digits = floor(log10(n)) + 1
        total_digits = int(log_val) + 1
        
        print(f"Step 2: 67^67^67 has approximately {total_digits:,} digits")
        print(f"        That's about {total_digits/1e9:.1f} billion digits!")
        
        # Calculate first digits using fractional part of logarithm
        frac_part = log_val - int(log_val)
        first_digits_decimal = Decimal(10) ** frac_part
        
        # Extract first 50 digits
        first_digits_str = str(first_digits_decimal).replace('.', '')
        first_50 = first_digits_str[:50]
        
        return total_digits, first_50, inner_exp
        
    finally:
        getcontext().prec = original_prec

def display_results(total_digits, first_digits, inner_exp):
    """Display the results in a nice format"""
    print("\n" + "="*70)
    print("RESULTS FOR 67^67^67")
    print("="*70)
    
    print(f"Exponent (67^67): {inner_exp}")
    print(f"Total digits:     {total_digits:,}")
    print(f"First 50 digits:  {first_digits}")
    
    # Format first digits in groups of 10
    print("\nFirst digits grouped:")
    for i in range(0, min(50, len(first_digits)), 10):
        group = first_digits[i:i+10]
        print(f"  {i//10 + 1:2d}: {group}")
    
    # Put this in perspective
    print(f"\nTo put this in perspective:")
    print(f"- Observable universe has ~10^80 atoms")
    print(f"- 67^67^67 has ~10^{len(str(total_digits))} digits")
    print(f"- If each digit were an atom, you'd need")
    print(f"  {total_digits // (10**80):,} universes to store this number!")
    
    # Save to file
    filename = "67_power_tower_result.txt"
    with open(filename, "w") as f:
        f.write("67^67^67 Calculation Results\n")
        f.write("="*30 + "\n\n")
        f.write(f"67^67 = {inner_exp}\n\n")
        f.write(f"Total digits in 67^67^67: {total_digits:,}\n\n")
        f.write(f"First 50 digits: {first_digits}\n\n")
        f.write("Note: This number is so large that computing all its digits\n")
        f.write("would be physically impossible with current technology.\n")
    
    print(f"\nResults saved to: {filename}")

def main():
    """Main function"""
    try:
        print("67^67^67 Calculator")
        print("="*50)
        print("This calculates the astronomically large number 67^67^67")
        print("using logarithmic methods to find its properties.\n")
        
        total_digits, first_digits, inner_exp = calculate_67_power_tower()
        display_results(total_digits, first_digits, inner_exp)
        
        print("\n" + "="*70)
        print("Calculation completed successfully!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
