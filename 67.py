import os
import lzma
import math
from decimal import Decimal, getcontext

# ------------------ CONFIGURATION ------------------
CHUNK_SIZE = 10000000000000000000000000000000000000000000000000000000000      # digits per chunk
MAX_BYTES = 10000000       # target compressed size per chunk
KEEP_INTERMEDIATE = True
STORE_DIR = "chunks_store"
os.makedirs(STORE_DIR, exist_ok=True)

# Number parameters
BASE = 67
EXP = 67 ** 67  # astronomical; for testing, use smaller exponent

# Decimal precision for leading digits
getcontext().prec = CHUNK_SIZE + 50

# ------------------ UTILITIES ------------------

def chunk_leading_digits(base, exp, start_digit, chunk_size):
    """
    Generate a chunk of digits starting at start_digit (1-indexed) using logs.
    """
    log_val = Decimal(exp) * Decimal(base).ln() / Decimal(math.log(10))
    total_digits = int(log_val) + 1

    if start_digit > total_digits:
        return ''

    digits_to_compute = min(chunk_size, total_digits - start_digit + 1)
    frac_part = log_val - int(log_val)
    first_digit_exponent = frac_part + (start_digit - 1)
    chunk = int((Decimal(10) ** first_digit_exponent) * (10 ** digits_to_compute))
    return str(chunk)[:digits_to_compute]

def compress_until_fit(chunk, max_bytes, base_name):
    """
    Compress chunk using LZMA repeatedly until <= max_bytes.
    Warn if compression increases size.
    """
    current_file = os.path.join(STORE_DIR, f"{base_name}.txt")
    with open(current_file, "w") as f:
        f.write(chunk)

    level = 0
    prev_size = os.path.getsize(current_file)

    while prev_size > max_bytes or level == 0:
        level += 1
        new_file = os.path.join(STORE_DIR, f"{base_name}_level{level}.xz")
        with lzma.open(new_file, "wb") as f:
            with open(current_file, "rb") as orig:
                f.write(orig.read())

        new_size = os.path.getsize(new_file)

        if new_size > prev_size:
            print(f"Warning: compression increased size ({prev_size}->{new_size}) for {current_file}")
            confirm = input("Continue compressing this chunk? (y/n): ").strip().lower()
            if confirm != 'y':
                return new_file, level

        if not KEEP_INTERMEDIATE:
            os.remove(current_file)

        current_file = new_file
        prev_size = new_size

    return current_file, level

def decompress_chunk(file_path):
    """
    Decompress LZMA file back to original text.
    """
    current_file = file_path
    while current_file.endswith(".xz"):
        new_txt = current_file.replace(".xz", ".txt")
        with lzma.open(current_file, "rb") as f_in:
            with open(new_txt, "wb") as f_out:
                f_out.write(f_in.read())
        current_file = new_txt
    with open(current_file, "r") as f:
        return f.read()

# ------------------ MAIN ------------------

def main():
    log_val = Decimal(EXP) * Decimal(BASE).ln() / Decimal(math.log(10))
    total_digits = int(log_val) + 1
    total_chunks = (total_digits + CHUNK_SIZE - 1) // CHUNK_SIZE
    print(f"Total digits: {total_digits}, total chunks: {total_chunks}")

    for i in range(total_chunks):
        start_digit = i * CHUNK_SIZE + 1
        chunk = chunk_leading_digits(BASE, EXP, start_digit, CHUNK_SIZE)
        if not chunk:
            continue

        final_file, levels = compress_until_fit(chunk, MAX_BYTES, f"chunk_{i}")
        print(f"Chunk {i} saved as {final_file}, compressed {levels} times")

    # Demo: decompress first chunk
    first_file = os.path.join(STORE_DIR, "chunk_0_level1.xz")
    if os.path.exists(first_file):
        print("\nDecompressing first chunk to verify:")
        data = decompress_chunk(first_file)
        print(data)
    else:
        print("\nFirst compressed file not found (chunk smaller than MAX_BYTES?)")

if __name__ == "__main__":
    main()
